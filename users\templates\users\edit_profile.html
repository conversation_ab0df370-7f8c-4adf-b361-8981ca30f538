{% extends 'base.html' %}

{% block title %}Edit Profile - TemplateHub{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900">Edit Profile</h2>
            <p class="text-gray-600 mt-1">Update your account information and profile details</p>
        </div>
        
        <form method="post" enctype="multipart/form-data" class="p-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Account Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                    
                    <!-- First Name -->
                    <div class="mb-4">
                        <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            First Name
                        </label>
                        {{ user_form.first_name }}
                        {% if user_form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ user_form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Last Name -->
                    <div class="mb-4">
                        <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Last Name
                        </label>
                        {{ user_form.last_name }}
                        {% if user_form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ user_form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Username -->
                    <div class="mb-4">
                        <label for="{{ user_form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Username
                        </label>
                        {{ user_form.username }}
                        {% if user_form.username.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ user_form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Email -->
                    <div class="mb-4">
                        <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        {{ user_form.email }}
                        {% if user_form.email.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ user_form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <!-- Profile Information -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Profile Information</h3>
                    
                    <!-- Avatar -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Profile Picture
                        </label>
                        <div class="flex items-center space-x-4">
                            <img class="h-16 w-16 rounded-full" 
                                 src="{% if user.userprofile.avatar %}{{ user.userprofile.avatar.url }}{% else %}https://ui-avatars.com/api/?name={{ user.get_full_name|default:user.username }}&background=3b82f6&color=fff&size=64{% endif %}" 
                                 alt="{{ user.get_full_name|default:user.username }}">
                            <div class="flex-1">
                                {{ profile_form.avatar }}
                                {% if profile_form.avatar.errors %}
                                    <div class="mt-1 text-sm text-red-600">
                                        {{ profile_form.avatar.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Bio -->
                    <div class="mb-4">
                        <label for="{{ profile_form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Bio
                        </label>
                        {{ profile_form.bio }}
                        {% if profile_form.bio.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ profile_form.bio.errors.0 }}
                            </div>
                        {% endif %}
                        <p class="mt-1 text-sm text-gray-500">Tell us a bit about yourself</p>
                    </div>
                    
                    <!-- Location -->
                    <div class="mb-4">
                        <label for="{{ profile_form.location.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Location
                        </label>
                        {{ profile_form.location }}
                        {% if profile_form.location.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ profile_form.location.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- Website -->
                    <div class="mb-4">
                        <label for="{{ profile_form.website.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            Website
                        </label>
                        {{ profile_form.website }}
                        {% if profile_form.website.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ profile_form.website.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- GitHub URL -->
                    <div class="mb-4">
                        <label for="{{ profile_form.github_url.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            GitHub URL
                        </label>
                        {{ profile_form.github_url }}
                        {% if profile_form.github_url.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ profile_form.github_url.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- LinkedIn URL -->
                    <div class="mb-4">
                        <label for="{{ profile_form.linkedin_url.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-2">
                            LinkedIn URL
                        </label>
                        {{ profile_form.linkedin_url }}
                        {% if profile_form.linkedin_url.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ profile_form.linkedin_url.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <a href="{% url 'users:profile' %}" class="bg-gray-300 text-gray-700 px-6 py-2 rounded-md font-medium hover:bg-gray-400 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" class="bg-primary-600 text-white px-6 py-2 rounded-md font-medium hover:bg-primary-700 transition-colors">
                        Save Changes
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}
