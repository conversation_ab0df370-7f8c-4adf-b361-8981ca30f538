{% extends 'base.html' %}

{% block title %}Sign In - TemplateHub{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <i class="fas fa-code text-primary-600 text-4xl mb-4"></i>
            <h2 class="text-3xl font-bold text-gray-900">Welcome Back</h2>
            <p class="mt-2 text-gray-600">Sign in to your TemplateHub account</p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form class="space-y-6" method="post">
                {% csrf_token %}
                
                <!-- Username -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Username
                    </label>
                    <div class="mt-1">
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Password -->
                <div>
                    <label for="{{ form.password.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <div class="mt-1">
                        {{ form.password }}
                        {% if form.password.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.password.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input id="remember-me" name="remember-me" type="checkbox" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
                            Remember me
                        </label>
                    </div>

                    <div class="text-sm">
                        <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
                            Forgot your password?
                        </a>
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Sign In
                    </button>
                </div>

                <!-- Non-field errors -->
                {% if form.non_field_errors %}
                    <div class="mt-3 text-sm text-red-600">
                        {{ form.non_field_errors.0 }}
                    </div>
                {% endif %}
            </form>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Don't have an account?</span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{% url 'users:register' %}" class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Create Account
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
