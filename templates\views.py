from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse

# Create your views here.

def home(request):
    return HttpResponse("TemplateHub Home - Coming Soon!")

def template_list(request):
    return HttpResponse("Template List - Coming Soon!")

def template_detail(request, pk):
    return HttpResponse(f"Template Detail {pk} - Coming Soon!")

def template_upload(request):
    return HttpResponse("Template Upload - Coming Soon!")

def category_templates(request, slug):
    return HttpResponse(f"Category {slug} Templates - Coming Soon!")

def search_templates(request):
    return HttpResponse("Search Templates - Coming Soon!")

def download_template(request, pk):
    return HttpResponse(f"Download Template {pk} - Coming Soon!")
