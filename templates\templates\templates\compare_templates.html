{% extends 'base.html' %}

{% block title %}Compare Templates - TemplateHub{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">Compare Templates</h1>
        <p class="text-gray-600">Compare features, pricing, and details of multiple templates side by side</p>
    </div>
    
    <!-- Template Selector -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Select Templates to Compare</h2>
        <div class="flex flex-wrap gap-4">
            <input type="text" id="template-search" placeholder="Search templates..." 
                   class="flex-1 min-w-64 px-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500">
            <button onclick="searchTemplates()" class="bg-primary-600 text-white px-6 py-2 rounded-md hover:bg-primary-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
        </div>
        
        <!-- Search Results -->
        <div id="search-results" class="mt-4 hidden">
            <h3 class="text-md font-medium text-gray-900 mb-2">Search Results:</h3>
            <div id="results-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"></div>
        </div>
        
        <!-- Selected Templates -->
        <div id="selected-templates" class="mt-6">
            <h3 class="text-md font-medium text-gray-900 mb-2">Selected for Comparison:</h3>
            <div id="selected-container" class="flex flex-wrap gap-2"></div>
        </div>
    </div>
    
    <!-- Comparison Table -->
    {% if templates %}
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feature</th>
                            {% for template in templates %}
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    <div class="text-center">
                                        {% if template.preview_image %}
                                            <img src="{{ template.preview_image.url }}" alt="{{ template.title }}" class="w-20 h-20 object-cover rounded-lg mx-auto mb-2">
                                        {% else %}
                                            <div class="w-20 h-20 bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg flex items-center justify-center mx-auto mb-2">
                                                <i class="fas fa-code text-primary-600"></i>
                                            </div>
                                        {% endif %}
                                        <div class="font-semibold text-gray-900">{{ template.title|truncatechars:20 }}</div>
                                    </div>
                                </th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Title -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Title</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">{{ template.title }}</td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Category -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Category</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <span class="bg-primary-100 text-primary-800 px-2 py-1 rounded text-xs">{{ template.category.name }}</span>
                                </td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Price -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Price</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    {% if template.is_free %}
                                        <span class="text-green-600 font-semibold">Free</span>
                                    {% else %}
                                        <span class="text-gray-900 font-semibold">${{ template.price }}</span>
                                    {% endif %}
                                </td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Difficulty -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Difficulty</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <span class="capitalize">{{ template.difficulty }}</span>
                                </td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Rating -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Rating</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div class="flex items-center">
                                        <div class="flex items-center text-yellow-400 mr-2">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= template.average_rating %}
                                                    <i class="fas fa-star text-xs"></i>
                                                {% else %}
                                                    <i class="far fa-star text-xs"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="text-gray-600 text-xs">({{ template.review_count }})</span>
                                    </div>
                                </td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Downloads -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Downloads</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">{{ template.download_count }}</td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Author -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Author</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">{{ template.author.username }}</td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Version -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Version</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">{{ template.version }}</td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Tags -->
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Tags</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div class="flex flex-wrap gap-1">
                                        {% for tag in template.tags.all|slice:":3" %}
                                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">{{ tag.name }}</span>
                                        {% endfor %}
                                    </div>
                                </td>
                            {% endfor %}
                        </tr>
                        
                        <!-- Actions -->
                        <tr class="bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Actions</td>
                            {% for template in templates %}
                                <td class="px-6 py-4 text-sm text-gray-900">
                                    <div class="flex flex-col space-y-2">
                                        <a href="{{ template.get_absolute_url }}" class="bg-primary-600 text-white px-3 py-1 rounded text-xs text-center hover:bg-primary-700 transition-colors">
                                            View Details
                                        </a>
                                        {% if template.demo_url %}
                                            <a href="{{ template.demo_url }}" target="_blank" class="bg-gray-200 text-gray-800 px-3 py-1 rounded text-xs text-center hover:bg-gray-300 transition-colors">
                                                Live Demo
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            {% endfor %}
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="bg-white rounded-lg shadow-md p-12 text-center">
            <i class="fas fa-balance-scale text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Templates Selected</h3>
            <p class="text-gray-600 mb-6">Search and select templates above to start comparing their features</p>
            <a href="{% url 'templates:template_list' %}" class="bg-primary-600 text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors">
                Browse Templates
            </a>
        </div>
    {% endif %}
</div>

<script>
let selectedTemplates = {{ template_ids|default:"[]"|safe }};

function searchTemplates() {
    const query = document.getElementById('template-search').value;
    if (!query.trim()) return;
    
    fetch(`/browse/?q=${encodeURIComponent(query)}`)
        .then(response => response.text())
        .then(html => {
            // Parse the response to extract template data
            // This is a simplified version - in production, you'd want a proper API
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const templateCards = doc.querySelectorAll('.bg-white.rounded-lg.shadow-md');
            
            const resultsContainer = document.getElementById('results-container');
            resultsContainer.innerHTML = '';
            
            templateCards.forEach((card, index) => {
                if (index < 6) { // Limit results
                    const title = card.querySelector('h3').textContent.trim();
                    const link = card.querySelector('a[href*="/template/"]');
                    const templateId = link ? link.href.match(/\/template\/(\d+)\//)[1] : null;
                    
                    if (templateId) {
                        const resultCard = document.createElement('div');
                        resultCard.className = 'border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer';
                        resultCard.innerHTML = `
                            <h4 class="font-medium text-gray-900 mb-2">${title}</h4>
                            <button onclick="addToComparison(${templateId}, '${title}')" 
                                    class="w-full bg-primary-600 text-white px-3 py-1 rounded text-sm hover:bg-primary-700 transition-colors">
                                Add to Compare
                            </button>
                        `;
                        resultsContainer.appendChild(resultCard);
                    }
                }
            });
            
            document.getElementById('search-results').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function addToComparison(templateId, title) {
    if (selectedTemplates.includes(templateId.toString())) {
        alert('Template already selected for comparison');
        return;
    }
    
    if (selectedTemplates.length >= 4) {
        alert('Maximum 4 templates can be compared at once');
        return;
    }
    
    selectedTemplates.push(templateId.toString());
    updateSelectedDisplay();
    updateComparisonUrl();
}

function removeFromComparison(templateId) {
    selectedTemplates = selectedTemplates.filter(id => id !== templateId.toString());
    updateSelectedDisplay();
    updateComparisonUrl();
}

function updateSelectedDisplay() {
    const container = document.getElementById('selected-container');
    container.innerHTML = '';
    
    selectedTemplates.forEach(templateId => {
        fetch(`/api/template/${templateId}/`)
            .then(response => response.json())
            .then(data => {
                const tag = document.createElement('span');
                tag.className = 'bg-primary-100 text-primary-800 px-3 py-1 rounded-full text-sm flex items-center';
                tag.innerHTML = `
                    ${data.title}
                    <button onclick="removeFromComparison(${templateId})" class="ml-2 text-primary-600 hover:text-primary-800">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(tag);
            });
    });
}

function updateComparisonUrl() {
    if (selectedTemplates.length > 0) {
        const params = selectedTemplates.map(id => `templates=${id}`).join('&');
        window.history.replaceState({}, '', `?${params}`);
        
        if (selectedTemplates.length >= 2) {
            // Show compare button
            let compareBtn = document.getElementById('compare-btn');
            if (!compareBtn) {
                compareBtn = document.createElement('button');
                compareBtn.id = 'compare-btn';
                compareBtn.className = 'mt-4 bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 transition-colors';
                compareBtn.innerHTML = '<i class="fas fa-balance-scale mr-2"></i>Compare Selected Templates';
                compareBtn.onclick = () => window.location.reload();
                document.getElementById('selected-templates').appendChild(compareBtn);
            }
        }
    }
}

// Initialize display
if (selectedTemplates.length > 0) {
    updateSelectedDisplay();
}

// Enter key search
document.getElementById('template-search').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        searchTemplates();
    }
});
</script>
{% endblock %}
