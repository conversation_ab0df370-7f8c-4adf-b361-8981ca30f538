{% extends 'base.html' %}

{% block title %}Sign Up - TemplateHub{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="text-center">
            <i class="fas fa-code text-primary-600 text-4xl mb-4"></i>
            <h2 class="text-3xl font-bold text-gray-900">Join TemplateHub</h2>
            <p class="mt-2 text-gray-600">Create your account to start sharing templates</p>
        </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <form class="space-y-6" method="post">
                {% csrf_token %}
                
                <!-- First Name -->
                <div>
                    <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        First Name
                    </label>
                    <div class="mt-1">
                        {{ form.first_name }}
                        {% if form.first_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.first_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Last Name -->
                <div>
                    <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Last Name
                    </label>
                    <div class="mt-1">
                        {{ form.last_name }}
                        {% if form.last_name.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.last_name.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Username -->
                <div>
                    <label for="{{ form.username.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Username
                    </label>
                    <div class="mt-1">
                        {{ form.username }}
                        {% if form.username.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.username.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Email -->
                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Email Address
                    </label>
                    <div class="mt-1">
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.email.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Password -->
                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Password
                    </label>
                    <div class="mt-1">
                        {{ form.password1 }}
                        {% if form.password1.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.password1.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Confirm Password -->
                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        Confirm Password
                    </label>
                    <div class="mt-1">
                        {{ form.password2 }}
                        {% if form.password2.errors %}
                            <div class="mt-1 text-sm text-red-600">
                                {{ form.password2.errors.0 }}
                            </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Submit Button -->
                <div>
                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Create Account
                    </button>
                </div>
            </form>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-500">Already have an account?</span>
                    </div>
                </div>

                <div class="mt-6">
                    <a href="{% url 'users:login' %}" class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        Sign In
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
