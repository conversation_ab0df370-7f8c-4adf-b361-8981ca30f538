{% extends 'base.html' %}

{% block title %}TemplateHub - Digital Code Template Marketplace{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-primary-600 to-primary-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                Find Perfect Code Templates
            </h1>
            <p class="text-xl text-primary-100 mb-8 max-w-3xl mx-auto">
                Discover, download, and share high-quality code templates for your next project. 
                From HTML to React, Django to Vue.js - we have everything you need.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="{% url 'templates:template_list' %}"
                   class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                    Browse Templates
                </a>
                {% if user.is_authenticated %}
                    <a href="{% url 'templates:template_upload' %}"
                       class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                        Upload Template
                    </a>
                {% else %}
                    <a href="{% url 'users:register' %}"
                       class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                        Join Community
                    </a>
                {% endif %}
            </div>

            <!-- Quick Links -->
            <div class="mt-8 flex flex-wrap justify-center gap-4 text-primary-100">
                <a href="{% url 'templates:trending_templates' %}" class="hover:text-white transition-colors">
                    <i class="fas fa-fire mr-1"></i>Trending
                </a>
                <a href="{% url 'templates:recent_templates' %}" class="hover:text-white transition-colors">
                    <i class="fas fa-clock mr-1"></i>Recent
                </a>
                {% if user.is_authenticated %}
                    <a href="{% url 'templates:user_favorites' %}" class="hover:text-white transition-colors">
                        <i class="fas fa-heart mr-1"></i>My Favorites
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="bg-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl font-bold text-primary-600">{{ total_templates|default:"500+" }}</div>
                <div class="text-gray-600 mt-2">Templates</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-primary-600">{{ total_downloads|default:"10K+" }}</div>
                <div class="text-gray-600 mt-2">Downloads</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-primary-600">{{ total_users|default:"1K+" }}</div>
                <div class="text-gray-600 mt-2">Developers</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-primary-600">{{ total_categories|default:"20+" }}</div>
                <div class="text-gray-600 mt-2">Categories</div>
            </div>
        </div>
    </div>
</div>

<!-- Featured Templates -->
<div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Featured Templates</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Handpicked templates from our community of talented developers
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% for template in featured_templates %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    {% if template.preview_image %}
                        <img src="{{ template.preview_image.url }}" alt="{{ template.title }}" class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                            <i class="fas fa-code text-primary-600 text-4xl"></i>
                        </div>
                    {% endif %}
                    
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                {{ template.category.name }}
                            </span>
                            <div class="flex items-center text-yellow-400">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= template.average_rating %}
                                        <i class="fas fa-star"></i>
                                    {% else %}
                                        <i class="far fa-star"></i>
                                    {% endif %}
                                {% endfor %}
                                <span class="text-gray-600 text-sm ml-1">({{ template.review_count }})</span>
                            </div>
                        </div>
                        
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ template.title }}</h3>
                        <p class="text-gray-600 text-sm mb-4">{{ template.short_description|truncatewords:15 }}</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-download mr-1"></i>
                                {{ template.download_count }}
                            </div>
                            <a href="{{ template.get_absolute_url }}" 
                               class="bg-primary-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-primary-700 transition-colors">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            {% empty %}
                <!-- Placeholder cards when no templates exist -->
                {% for i in "123" %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <i class="fas fa-code text-gray-400 text-4xl"></i>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <span class="bg-gray-100 text-gray-600 text-xs font-medium px-2.5 py-0.5 rounded">
                                    Coming Soon
                                </span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">Sample Template {{ forloop.counter }}</h3>
                            <p class="text-gray-600 text-sm mb-4">Amazing templates are coming soon to TemplateHub!</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-gray-500">
                                    <i class="fas fa-download mr-1"></i>
                                    0
                                </div>
                                <button class="bg-gray-300 text-gray-500 px-4 py-2 rounded text-sm font-medium cursor-not-allowed">
                                    Coming Soon
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
        
        <div class="text-center mt-12">
            <a href="{% url 'templates:template_list' %}" 
               class="bg-primary-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors">
                View All Templates
            </a>
        </div>
    </div>
</div>

<!-- Categories Section -->
<div class="bg-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Browse by Category</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">
                Find templates organized by technology and framework
            </p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
            {% for category in categories %}
                <a href="{{ category.get_absolute_url }}" 
                   class="group bg-gray-50 rounded-lg p-6 text-center hover:bg-primary-50 hover:border-primary-200 border border-transparent transition-all">
                    {% if category.icon %}
                        <i class="{{ category.icon }} text-3xl text-gray-600 group-hover:text-primary-600 mb-3"></i>
                    {% else %}
                        <i class="fas fa-folder text-3xl text-gray-600 group-hover:text-primary-600 mb-3"></i>
                    {% endif %}
                    <h3 class="font-medium text-gray-900 group-hover:text-primary-600">{{ category.name }}</h3>
                    <p class="text-sm text-gray-500 mt-1">{{ category.templates.count }} templates</p>
                </a>
            {% empty %}
                <!-- Placeholder categories -->
                {% for category in "HTML,React,Vue.js,Django,Laravel,Angular" %}
                    <div class="bg-gray-50 rounded-lg p-6 text-center border border-transparent">
                        <i class="fas fa-folder text-3xl text-gray-400 mb-3"></i>
                        <h3 class="font-medium text-gray-600">{{ category }}</h3>
                        <p class="text-sm text-gray-500 mt-1">Coming soon</p>
                    </div>
                {% endfor %}
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
