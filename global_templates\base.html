<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TemplateHub - Digital Code Template Marketplace{% endblock %}</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Inter', sans-serif; }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <!-- Logo -->
                    <a href="{% url 'templates:home' %}" class="flex items-center">
                        <i class="fas fa-code text-primary-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">TemplateHub</span>
                    </a>
                    
                    <!-- Main Navigation -->
                    <div class="hidden md:ml-10 md:flex md:space-x-8">
                        <a href="{% url 'templates:home' %}" class="text-gray-900 hover:text-primary-600 px-3 py-2 text-sm font-medium">Home</a>
                        <a href="{% url 'templates:template_list' %}" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium">Browse Templates</a>
                        <a href="#" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium">Categories</a>
                    </div>
                </div>
                
                <!-- Search Bar -->
                <div class="flex-1 flex items-center justify-center px-2 lg:ml-6 lg:justify-end">
                    <div class="max-w-lg w-full lg:max-w-xs">
                        <form action="{% url 'templates:search_templates' %}" method="GET" class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>
                            <input type="text" name="q" placeholder="Search templates..." 
                                   class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm">
                        </form>
                    </div>
                </div>
                
                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    {% if user.is_authenticated %}
                        <a href="{% url 'templates:template_upload' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
                            <i class="fas fa-upload mr-2"></i>Upload
                        </a>
                        <div class="relative">
                            <button type="button" class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" id="user-menu-button">
                                <img class="h-8 w-8 rounded-full" src="https://ui-avatars.com/api/?name={{ user.username }}&background=3b82f6&color=fff" alt="{{ user.username }}">
                            </button>
                        </div>
                    {% else %}
                        <a href="{% url 'users:login' %}" class="text-gray-500 hover:text-primary-600 px-3 py-2 text-sm font-medium">Login</a>
                        <a href="{% url 'users:register' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">Sign Up</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mt-4">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} bg-{{ message.tags }}-100 border border-{{ message.tags }}-400 text-{{ message.tags }}-700 px-4 py-3 rounded mb-4">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Main Content -->
    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 mt-20">
        <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center">
                        <i class="fas fa-code text-primary-600 text-2xl mr-2"></i>
                        <span class="text-xl font-bold text-gray-900">TemplateHub</span>
                    </div>
                    <p class="mt-4 text-gray-600">
                        Your premier destination for high-quality digital code templates. 
                        Find, share, and download templates for all your development needs.
                    </p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Categories</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">HTML Templates</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">React Components</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Vue.js Templates</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Django Apps</a></li>
                    </ul>
                </div>
                <div>
                    <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase">Support</h3>
                    <ul class="mt-4 space-y-2">
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Help Center</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Contact Us</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary-600">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-200 pt-8">
                <p class="text-center text-gray-600">&copy; 2025 TemplateHub. All rights reserved.</p>
            </div>
        </div>
    </footer>

    {% block extra_js %}{% endblock %}
</body>
</html>
