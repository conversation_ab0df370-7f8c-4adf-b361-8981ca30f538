{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "scripts": {"build-css": "tailwindcss -i ./static/css/input.css -o ./static/css/output.css --watch", "build-css-prod": "tailwindcss -i ./static/css/input.css -o ./static/css/output.css --minify", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "tailwindcss": "^4.1.10"}}