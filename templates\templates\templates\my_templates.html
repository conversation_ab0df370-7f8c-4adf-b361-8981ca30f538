{% extends 'base.html' %}

{% block title %}My Templates - TemplateHub{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">My Templates</h1>
        <p class="text-gray-600">Manage all your uploaded templates</p>
    </div>
    
    <!-- Status Summary -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <div class="text-2xl font-bold text-blue-600">{{ templates|length }}</div>
            <div class="text-gray-600">Total Templates</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <div class="text-2xl font-bold text-green-600">
                {% for template in templates %}{% if template.status == 'published' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
            </div>
            <div class="text-gray-600">Published</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <div class="text-2xl font-bold text-yellow-600">
                {% for template in templates %}{% if template.status == 'pending' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
            </div>
            <div class="text-gray-600">Pending Review</div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6 text-center">
            <div class="text-2xl font-bold text-gray-600">
                {% for template in templates %}{% if template.status == 'draft' %}{{ forloop.counter0|add:1 }}{% endif %}{% empty %}0{% endfor %}
            </div>
            <div class="text-gray-600">Drafts</div>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="flex items-center justify-between mb-6">
        <div>
            {% if page_obj.paginator.count %}
                <p class="text-gray-600">
                    Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} template{{ page_obj.paginator.count|pluralize }}
                </p>
            {% endif %}
        </div>
        
        <a href="{% url 'templates:template_upload' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
            <i class="fas fa-plus mr-2"></i>Upload New Template
        </a>
    </div>
    
    <!-- Templates Grid -->
    {% if templates %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {% for template in templates %}
                <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                    <!-- Status Badge -->
                    <div class="absolute top-4 right-4 z-10">
                        {% if template.status == 'published' %}
                            <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <i class="fas fa-check-circle mr-1"></i>Published
                            </span>
                        {% elif template.status == 'pending' %}
                            <span class="bg-yellow-100 text-yellow-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <i class="fas fa-clock mr-1"></i>Pending
                            </span>
                        {% elif template.status == 'draft' %}
                            <span class="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <i class="fas fa-edit mr-1"></i>Draft
                            </span>
                        {% elif template.status == 'rejected' %}
                            <span class="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                                <i class="fas fa-times-circle mr-1"></i>Rejected
                            </span>
                        {% endif %}
                    </div>
                    
                    <!-- Preview Image -->
                    {% if template.preview_image %}
                        <img src="{{ template.preview_image.url }}" alt="{{ template.title }}" class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                            <i class="fas fa-code text-primary-600 text-4xl"></i>
                        </div>
                    {% endif %}
                    
                    <!-- Template Info -->
                    <div class="p-4">
                        <!-- Category and Rating -->
                        <div class="flex items-center justify-between mb-2">
                            <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                {{ template.category.name }}
                            </span>
                            {% if template.status == 'published' %}
                                <div class="flex items-center text-yellow-400">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= template.average_rating %}
                                            <i class="fas fa-star text-xs"></i>
                                        {% else %}
                                            <i class="far fa-star text-xs"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="text-gray-600 text-xs ml-1">({{ template.review_count }})</span>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Title -->
                        <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ template.title }}</h3>
                        
                        <!-- Description -->
                        <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ template.short_description }}</p>
                        
                        <!-- Stats -->
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                            <div class="flex items-center">
                                <i class="fas fa-calendar mr-1"></i>
                                {{ template.created_at|date:"M d, Y" }}
                            </div>
                            {% if template.status == 'published' %}
                                <div class="flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    {{ template.download_count }}
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Tags -->
                        {% if template.tags.all %}
                            <div class="flex flex-wrap gap-1 mb-3">
                                {% for tag in template.tags.all|slice:":3" %}
                                    <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">{{ tag.name }}</span>
                                {% endfor %}
                                {% if template.tags.count > 3 %}
                                    <span class="bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">+{{ template.tags.count|add:"-3" }}</span>
                                {% endif %}
                            </div>
                        {% endif %}
                        
                        <!-- Price and Actions -->
                        <div class="flex items-center justify-between">
                            <div>
                                {% if template.is_free %}
                                    <span class="text-green-600 font-semibold">Free</span>
                                {% else %}
                                    <span class="text-gray-900 font-semibold">${{ template.price }}</span>
                                {% endif %}
                            </div>
                            <div class="flex space-x-2">
                                <a href="{{ template.get_absolute_url }}" 
                                   class="bg-primary-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-primary-700 transition-colors">
                                    View
                                </a>
                                {% if template.status == 'draft' %}
                                    <a href="#" 
                                       class="bg-gray-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-gray-700 transition-colors">
                                        Edit
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Status Message -->
                        {% if template.status == 'pending' %}
                            <div class="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs text-yellow-800">
                                <i class="fas fa-info-circle mr-1"></i>
                                Your template is under review. You'll be notified once it's approved.
                            </div>
                        {% elif template.status == 'rejected' %}
                            <div class="mt-3 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-800">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                Template was rejected. Please review and resubmit.
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="flex items-center justify-center">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <i class="fas fa-code text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No templates yet</h3>
            <p class="text-gray-600 mb-6">Start sharing your code templates with the community!</p>
            <a href="{% url 'templates:template_upload' %}" class="bg-primary-600 text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors">
                Upload Your First Template
            </a>
        </div>
    {% endif %}
</div>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
{% endblock %}
