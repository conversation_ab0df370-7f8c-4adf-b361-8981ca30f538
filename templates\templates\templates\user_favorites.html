{% extends 'base.html' %}

{% block title %}My Favorites - TemplateHub{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">My Favorite Templates</h1>
        <p class="text-gray-600">Templates you've bookmarked for later reference</p>
    </div>
    
    <!-- Breadcrumb -->
    <nav class="flex mb-6" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{% url 'templates:home' %}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-primary-600">
                    <i class="fas fa-home mr-2"></i>Home
                </a>
            </li>
            <li>
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <a href="{% url 'users:profile' %}" class="text-sm font-medium text-gray-700 hover:text-primary-600">Profile</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                    <span class="text-sm font-medium text-gray-500">Favorites</span>
                </div>
            </li>
        </ol>
    </nav>
    
    <!-- Results Header -->
    <div class="flex items-center justify-between mb-6">
        <div>
            {% if page_obj.paginator.count %}
                <p class="text-gray-600">
                    Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} favorite{{ page_obj.paginator.count|pluralize }}
                </p>
            {% endif %}
        </div>
        
        <a href="{% url 'templates:template_list' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
            <i class="fas fa-search mr-2"></i>Browse More Templates
        </a>
    </div>
    
    <!-- Favorites Grid -->
    {% if favorites %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {% for favorite in favorites %}
                {% with template=favorite.template %}
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <!-- Preview Image -->
                        {% if template.preview_image %}
                            <img src="{{ template.preview_image.url }}" alt="{{ template.title }}" class="w-full h-48 object-cover">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                                <i class="fas fa-code text-primary-600 text-4xl"></i>
                            </div>
                        {% endif %}
                        
                        <!-- Template Info -->
                        <div class="p-4">
                            <!-- Category and Rating -->
                            <div class="flex items-center justify-between mb-2">
                                <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2.5 py-0.5 rounded">
                                    {{ template.category.name }}
                                </span>
                                <div class="flex items-center text-yellow-400">
                                    {% for i in "12345" %}
                                        {% if forloop.counter <= template.average_rating %}
                                            <i class="fas fa-star text-xs"></i>
                                        {% else %}
                                            <i class="far fa-star text-xs"></i>
                                        {% endif %}
                                    {% endfor %}
                                    <span class="text-gray-600 text-xs ml-1">({{ template.review_count }})</span>
                                </div>
                            </div>
                            
                            <!-- Title -->
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">{{ template.title }}</h3>
                            
                            <!-- Description -->
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ template.short_description }}</p>
                            
                            <!-- Author and Stats -->
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                <div class="flex items-center">
                                    <img class="h-6 w-6 rounded-full mr-2" 
                                         src="https://ui-avatars.com/api/?name={{ template.author.username }}&background=3b82f6&color=fff&size=24" 
                                         alt="{{ template.author.username }}">
                                    <span>{{ template.author.username }}</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-download mr-1"></i>
                                    {{ template.download_count }}
                                </div>
                            </div>
                            
                            <!-- Favorited Date -->
                            <div class="text-xs text-gray-500 mb-3">
                                <i class="fas fa-heart mr-1 text-red-500"></i>
                                Favorited {{ favorite.created_at|date:"M d, Y" }}
                            </div>
                            
                            <!-- Price and Actions -->
                            <div class="flex items-center justify-between">
                                <div>
                                    {% if template.is_free %}
                                        <span class="text-green-600 font-semibold">Free</span>
                                    {% else %}
                                        <span class="text-gray-900 font-semibold">${{ template.price }}</span>
                                    {% endif %}
                                </div>
                                <div class="flex space-x-2">
                                    <button onclick="removeFavorite({{ template.pk }}, this)" 
                                            class="text-red-500 hover:text-red-700 p-1" 
                                            title="Remove from favorites">
                                        <i class="fas fa-heart"></i>
                                    </button>
                                    <a href="{{ template.get_absolute_url }}" 
                                       class="bg-primary-600 text-white px-3 py-1.5 rounded text-sm font-medium hover:bg-primary-700 transition-colors">
                                        View
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endwith %}
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
            <div class="flex items-center justify-center">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="relative inline-flex items-center px-4 py-2 border border-primary-500 bg-primary-50 text-sm font-medium text-primary-600">
                                {{ num }}
                            </span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <a href="?page={{ num }}" 
                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                                {{ num }}
                            </a>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" 
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </nav>
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="text-center py-12">
            <i class="fas fa-heart text-gray-400 text-6xl mb-4"></i>
            <h3 class="text-xl font-semibold text-gray-900 mb-2">No favorites yet</h3>
            <p class="text-gray-600 mb-6">Start exploring templates and save your favorites for easy access later!</p>
            <a href="{% url 'templates:template_list' %}" class="bg-primary-600 text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors">
                Browse Templates
            </a>
        </div>
    {% endif %}
</div>

<script>
function removeFavorite(templateId, button) {
    fetch(`/favorite/${templateId}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Content-Type': 'application/json',
        },
    })
    .then(response => response.json())
    .then(data => {
        // Remove the template card from the page
        const card = button.closest('.bg-white');
        card.style.transition = 'opacity 0.3s ease';
        card.style.opacity = '0';
        setTimeout(() => {
            card.remove();
            // Check if no more favorites
            const remainingCards = document.querySelectorAll('.bg-white.rounded-lg.shadow-md');
            if (remainingCards.length === 0) {
                location.reload(); // Reload to show empty state
            }
        }, 300);
    })
    .catch(error => {
        console.error('Error:', error);
    });
}
</script>

<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
{% endblock %}
