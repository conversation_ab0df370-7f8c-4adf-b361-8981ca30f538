{% extends 'base.html' %}

{% block title %}My Profile - TemplateHub{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Profile Sidebar -->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="px-6 py-8 text-center">
                    <img class="mx-auto h-32 w-32 rounded-full" 
                         src="{% if profile.avatar %}{{ profile.avatar.url }}{% else %}https://ui-avatars.com/api/?name={{ user.get_full_name|default:user.username }}&background=3b82f6&color=fff&size=128{% endif %}" 
                         alt="{{ user.get_full_name|default:user.username }}">
                    <h3 class="mt-4 text-xl font-semibold text-gray-900">{{ user.get_full_name|default:user.username }}</h3>
                    <p class="text-gray-600">@{{ user.username }}</p>
                    {% if profile.is_verified %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                            <i class="fas fa-check-circle mr-1"></i>
                            Verified
                        </span>
                    {% endif %}
                    
                    {% if profile.bio %}
                        <p class="mt-4 text-gray-600 text-sm">{{ profile.bio }}</p>
                    {% endif %}
                    
                    <div class="mt-6 space-y-2">
                        {% if profile.location %}
                            <div class="flex items-center justify-center text-sm text-gray-600">
                                <i class="fas fa-map-marker-alt mr-2"></i>
                                {{ profile.location }}
                            </div>
                        {% endif %}
                        
                        {% if profile.website %}
                            <div class="flex items-center justify-center text-sm">
                                <i class="fas fa-globe mr-2 text-gray-600"></i>
                                <a href="{{ profile.website }}" target="_blank" class="text-primary-600 hover:text-primary-700">Website</a>
                            </div>
                        {% endif %}
                        
                        {% if profile.github_url %}
                            <div class="flex items-center justify-center text-sm">
                                <i class="fab fa-github mr-2 text-gray-600"></i>
                                <a href="{{ profile.github_url }}" target="_blank" class="text-primary-600 hover:text-primary-700">GitHub</a>
                            </div>
                        {% endif %}
                        
                        {% if profile.linkedin_url %}
                            <div class="flex items-center justify-center text-sm">
                                <i class="fab fa-linkedin mr-2 text-gray-600"></i>
                                <a href="{{ profile.linkedin_url }}" target="_blank" class="text-primary-600 hover:text-primary-700">LinkedIn</a>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mt-6">
                        <a href="{% url 'users:edit_profile' %}" class="w-full bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
                            Edit Profile
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Stats -->
            <div class="mt-6 bg-white rounded-lg shadow-md p-6">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h4>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600">Templates</span>
                        <span class="font-semibold">{{ user_templates.count }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Total Downloads</span>
                        <span class="font-semibold">{{ user_templates|length|default:0 }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-600">Member Since</span>
                        <span class="font-semibold">{{ user.date_joined|date:"M Y" }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="lg:col-span-2">
            <!-- My Templates -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold text-gray-900">My Templates</h3>
                        <a href="{% url 'templates:template_upload' %}" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Upload New
                        </a>
                    </div>
                </div>
                
                <div class="p-6">
                    {% if user_templates %}
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% for template in user_templates %}
                                <div class="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                                    {% if template.preview_image %}
                                        <img src="{{ template.preview_image.url }}" alt="{{ template.title }}" class="w-full h-32 object-cover">
                                    {% else %}
                                        <div class="w-full h-32 bg-gradient-to-br from-primary-100 to-primary-200 flex items-center justify-center">
                                            <i class="fas fa-code text-primary-600 text-2xl"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="p-4">
                                        <div class="flex items-center justify-between mb-2">
                                            <span class="bg-primary-100 text-primary-800 text-xs font-medium px-2 py-1 rounded">
                                                {{ template.category.name }}
                                            </span>
                                            <span class="text-xs text-gray-500">{{ template.status|title }}</span>
                                        </div>
                                        
                                        <h4 class="font-semibold text-gray-900 mb-2">{{ template.title }}</h4>
                                        <p class="text-gray-600 text-sm mb-3">{{ template.short_description|truncatewords:10 }}</p>
                                        
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex items-center text-gray-500">
                                                <i class="fas fa-download mr-1"></i>
                                                {{ template.download_count }}
                                            </div>
                                            <a href="{{ template.get_absolute_url }}" class="text-primary-600 hover:text-primary-700 font-medium">
                                                View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-12">
                            <i class="fas fa-code text-gray-400 text-4xl mb-4"></i>
                            <h4 class="text-lg font-semibold text-gray-900 mb-2">No Templates Yet</h4>
                            <p class="text-gray-600 mb-6">Start sharing your code templates with the community!</p>
                            <a href="{% url 'templates:template_upload' %}" class="bg-primary-600 text-white px-6 py-3 rounded-md font-medium hover:bg-primary-700 transition-colors">
                                Upload Your First Template
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
